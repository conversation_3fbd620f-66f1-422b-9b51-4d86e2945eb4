import { useState } from 'react'
import { User, Mail, Phone, Calendar, MapPin, Edit, Save, X } from 'lucide-react'
import toast from 'react-hot-toast'

const Profile = () => {
  const [isEditing, setIsEditing] = useState(false)
  const [profileData, setProfileData] = useState({
    name: 'أحمد محمد علي',
    email: '<EMAIL>',
    phone: '01234567890',
    studentId: '2021170001',
    department: 'علوم الحاسب',
    level: 'المستوى الرابع',
    gpa: '3.85',
    completedHours: '120',
    totalHours: '132',
    address: 'القاهرة، مصر',
    birthDate: '2000-05-15',
    enrollmentDate: '2021-09-01'
  })

  const [editData, setEditData] = useState(profileData)

  const handleEdit = () => {
    setIsEditing(true)
    setEditData(profileData)
  }

  const handleSave = () => {
    setProfileData(editData)
    setIsEditing(false)
    toast.success('تم حفظ التغييرات بنجاح')
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditData(profileData)
  }

  const handleInputChange = (field, value) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const academicProgress = [
    { semester: 'الفصل الأول 2021', gpa: 3.2, hours: 18 },
    { semester: 'الفصل الثاني 2021', gpa: 3.5, hours: 18 },
    { semester: 'الفصل الأول 2022', gpa: 3.7, hours: 18 },
    { semester: 'الفصل الثاني 2022', gpa: 3.8, hours: 18 },
    { semester: 'الفصل الأول 2023', gpa: 3.9, hours: 18 },
    { semester: 'الفصل الثاني 2023', gpa: 4.0, hours: 18 },
    { semester: 'الفصل الأول 2024', gpa: 3.8, hours: 12 }
  ]

  const achievements = [
    { title: 'الطالب المتفوق', date: '2023', description: 'حصل على المركز الأول على الدفعة' },
    { title: 'مشروع متميز', date: '2023', description: 'أفضل مشروع في مادة هندسة البرمجيات' },
    { title: 'شهادة تقدير', date: '2022', description: 'للمشاركة في المسابقة البرمجية' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="flex justify-between items-center">
          <div className="flex space-x-2 rtl:space-x-reverse">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
                >
                  <Save className="h-4 w-4" />
                  <span>حفظ</span>
                </button>
                <button
                  onClick={handleCancel}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
                >
                  <X className="h-4 w-4" />
                  <span>إلغاء</span>
                </button>
              </>
            ) : (
              <button
                onClick={handleEdit}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
              >
                <Edit className="h-4 w-4" />
                <span>تعديل</span>
              </button>
            )}
          </div>
          <div className="text-right">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">الملف الشخصي</h1>
            <p className="text-gray-600">إدارة معلوماتك الشخصية والأكاديمية</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Personal Information */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">المعلومات الشخصية</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 text-right">الاسم الكامل</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                    dir="rtl"
                  />
                ) : (
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <User className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-900">{profileData.name}</span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 text-right">البريد الإلكتروني</label>
                {isEditing ? (
                  <input
                    type="email"
                    value={editData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                    dir="rtl"
                  />
                ) : (
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-900">{profileData.email}</span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 text-right">رقم الهاتف</label>
                {isEditing ? (
                  <input
                    type="tel"
                    value={editData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                    dir="rtl"
                  />
                ) : (
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-900">{profileData.phone}</span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 text-right">العنوان</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                    dir="rtl"
                  />
                ) : (
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <MapPin className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-900">{profileData.address}</span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 text-right">تاريخ الميلاد</label>
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-900">{profileData.birthDate}</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 text-right">تاريخ الالتحاق</label>
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-900">{profileData.enrollmentDate}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Academic Progress */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">التقدم الأكاديمي</h2>
            <div className="space-y-3">
              {academicProgress.map((semester, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <span className="text-sm font-medium text-blue-600">{semester.gpa}</span>
                    <span className="text-sm text-gray-600">{semester.hours} ساعة</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{semester.semester}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Academic Info & Achievements */}
        <div className="space-y-6">
          {/* Academic Information */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">المعلومات الأكاديمية</h2>
            <div className="space-y-4">
              <div className="text-right">
                <p className="text-sm text-gray-600">رقم الطالب</p>
                <p className="font-semibold text-gray-900">{profileData.studentId}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">القسم</p>
                <p className="font-semibold text-gray-900">{profileData.department}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">المستوى الدراسي</p>
                <p className="font-semibold text-gray-900">{profileData.level}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">المعدل التراكمي</p>
                <p className="font-semibold text-green-600 text-lg">{profileData.gpa}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">الساعات المكتملة</p>
                <p className="font-semibold text-gray-900">{profileData.completedHours} / {profileData.totalHours}</p>
              </div>
            </div>
          </div>

          {/* Achievements */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">الإنجازات</h2>
            <div className="space-y-4">
              {achievements.map((achievement, index) => (
                <div key={index} className="border-r-4 border-blue-500 pr-4">
                  <div className="flex justify-between items-start mb-1">
                    <span className="text-xs text-gray-500">{achievement.date}</span>
                    <h3 className="font-semibold text-gray-900 text-right">{achievement.title}</h3>
                  </div>
                  <p className="text-sm text-gray-600 text-right">{achievement.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Profile
