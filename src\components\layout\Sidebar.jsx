import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  BookOpen,
  Calculator,
  Library,
  Compass,
  QrCode,
  Star,
  User,
  ChevronRight
} from 'lucide-react'

const Sidebar = () => {
  const location = useLocation()

  const menuItems = [
    {
      title: 'الصفحة الرئيسية',
      icon: Home,
      path: '/',
      description: 'نظرة عامة على المنصة'
    },
    {
      title: 'دليل الكلية',
      icon: BookOpen,
      path: '/college-guide',
      description: 'الأقسام والبرامج الدراسية'
    },
    {
      title: 'حاسبة المعدل',
      icon: Calculator,
      path: '/gpa-calculator',
      description: 'حساب المعدل التراكمي'
    },
    {
      title: 'المصادر التعليمية',
      icon: Library,
      path: '/resources',
      description: 'محاضرات وكتب ومراجع'
    },
    {
      title: 'التوجيه الأكاديمي',
      icon: Compass,
      path: '/academic-guidance',
      description: 'المسارات والتخصصات'
    },
    {
      title: 'نظام الحضور',
      icon: QrCode,
      path: '/attendance',
      description: 'تسجيل وإدارة الحضور'
    },
    {
      title: 'تقييم الأساتذة',
      icon: Star,
      path: '/teacher-evaluation',
      description: 'تقييم أعضاء هيئة التدريس'
    },
    {
      title: 'الملف الشخصي',
      icon: User,
      path: '/profile',
      description: 'إعدادات الحساب'
    }
  ]

  const isActive = (path) => {
    return location.pathname === path
  }

  return (
    <aside className="fixed right-0 top-16 h-[calc(100vh-4rem)] w-64 bg-white shadow-lg border-l border-gray-200 overflow-y-auto">
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-800 mb-4 text-right">القائمة الرئيسية</h2>
        
        <nav className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon
            const active = isActive(item.path)
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`
                  group flex items-center justify-between p-3 rounded-lg transition-all duration-200
                  ${active 
                    ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-600' 
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
              >
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Icon 
                    className={`h-5 w-5 ${active ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'}`} 
                  />
                  <div className="text-right">
                    <div className={`font-medium ${active ? 'text-blue-700' : 'text-gray-900'}`}>
                      {item.title}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {item.description}
                    </div>
                  </div>
                </div>
                
                <ChevronRight 
                  className={`h-4 w-4 transition-transform duration-200 ${
                    active ? 'text-blue-600 rotate-180' : 'text-gray-400 group-hover:text-gray-600'
                  }`} 
                />
              </Link>
            )
          })}
        </nav>

        {/* Quick Stats */}
        <div className="mt-8 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
          <h3 className="text-sm font-semibold text-gray-800 mb-3 text-right">إحصائيات سريعة</h3>
          <div className="space-y-2 text-right">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">المعدل التراكمي</span>
              <span className="text-sm font-bold text-blue-600">3.85</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">الساعات المكتملة</span>
              <span className="text-sm font-bold text-green-600">120</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">المستوى الدراسي</span>
              <span className="text-sm font-bold text-purple-600">الرابع</span>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-semibold text-gray-800 mb-2 text-right">تحتاج مساعدة؟</h3>
          <p className="text-xs text-gray-600 text-right mb-3">
            تواصل مع فريق الدعم الفني للحصول على المساعدة
          </p>
          <button className="w-full bg-blue-600 text-white text-sm py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
            تواصل معنا
          </button>
        </div>
      </div>
    </aside>
  )
}

export default Sidebar
