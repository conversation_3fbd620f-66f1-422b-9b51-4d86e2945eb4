import { useState } from 'react'
import { Search, Filter, Download, Eye, Star, BookOpen, Video, FileText } from 'lucide-react'

const Resources = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedSubject, setSelectedSubject] = useState('all')

  const categories = [
    { id: 'all', name: 'جميع الفئات', icon: BookOpen },
    { id: 'lectures', name: 'محاضرات', icon: Video },
    { id: 'books', name: 'كتب ومراجع', icon: BookOpen },
    { id: 'exams', name: 'امتحانات سابقة', icon: FileText },
    { id: 'assignments', name: 'تكليفات', icon: FileText }
  ]

  const subjects = [
    { id: 'all', name: 'جميع المواد' },
    { id: 'programming', name: 'البرمجة' },
    { id: 'databases', name: 'قواعد البيانات' },
    { id: 'networks', name: 'الشبكات' },
    { id: 'ai', name: 'الذكاء الاصطناعي' },
    { id: 'security', name: 'أمن المعلومات' }
  ]

  const resources = [
    {
      id: 1,
      title: 'محاضرات البرمجة المتقدمة - الجزء الأول',
      subject: 'programming',
      category: 'lectures',
      type: 'video',
      rating: 4.8,
      downloads: 245,
      size: '120 MB',
      description: 'شرح مفصل لمفاهيم البرمجة المتقدمة مع أمثلة عملية'
    },
    {
      id: 2,
      title: 'كتاب أساسيات قواعد البيانات',
      subject: 'databases',
      category: 'books',
      type: 'pdf',
      rating: 4.6,
      downloads: 189,
      size: '15 MB',
      description: 'كتاب شامل يغطي جميع أساسيات قواعد البيانات'
    },
    {
      id: 3,
      title: 'امتحان منتصف الفصل - الشبكات',
      subject: 'networks',
      category: 'exams',
      type: 'pdf',
      rating: 4.3,
      downloads: 156,
      size: '2 MB',
      description: 'امتحان منتصف الفصل للعام الماضي مع الحلول'
    },
    {
      id: 4,
      title: 'تكليف مشروع الذكاء الاصطناعي',
      subject: 'ai',
      category: 'assignments',
      type: 'pdf',
      rating: 4.5,
      downloads: 98,
      size: '5 MB',
      description: 'تكليف مشروع عملي في مجال الذكاء الاصطناعي'
    },
    {
      id: 5,
      title: 'محاضرات أمن المعلومات - الفصل الثاني',
      subject: 'security',
      category: 'lectures',
      type: 'video',
      rating: 4.7,
      downloads: 203,
      size: '180 MB',
      description: 'محاضرات متقدمة في أمن المعلومات والحماية'
    }
  ]

  const filteredResources = resources.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || resource.category === selectedCategory
    const matchesSubject = selectedSubject === 'all' || resource.subject === selectedSubject
    
    return matchesSearch && matchesCategory && matchesSubject
  })

  const getTypeIcon = (type) => {
    switch (type) {
      case 'video': return Video
      case 'pdf': return FileText
      default: return BookOpen
    }
  }

  const renderStars = (rating) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />)
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="h-4 w-4 fill-yellow-400 text-yellow-400" />)
    }

    const emptyStars = 5 - Math.ceil(rating)
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="h-4 w-4 text-gray-300" />)
    }

    return stars
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="text-right">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">المصادر التعليمية</h1>
          <p className="text-gray-600 text-lg">
            مكتبة شاملة من المحاضرات، الكتب، والامتحانات السابقة
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="البحث في المصادر..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
              dir="rtl"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
          >
            {categories.map(category => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>

          {/* Subject Filter */}
          <select
            value={selectedSubject}
            onChange={(e) => setSelectedSubject(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
          >
            {subjects.map(subject => (
              <option key={subject.id} value={subject.id}>{subject.name}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Resources Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredResources.map(resource => {
          const TypeIcon = getTypeIcon(resource.type)
          return (
            <div key={resource.id} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <TypeIcon className="h-5 w-5 text-blue-600" />
                  <span className="text-sm text-gray-500 capitalize">{resource.type}</span>
                </div>
                <div className="text-right">
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {subjects.find(s => s.id === resource.subject)?.name}
                  </span>
                </div>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2 text-right line-clamp-2">
                {resource.title}
              </h3>

              <p className="text-gray-600 text-sm mb-4 text-right line-clamp-2">
                {resource.description}
              </p>

              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  {renderStars(resource.rating)}
                  <span className="text-sm text-gray-600 mr-1">({resource.rating})</span>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600">{resource.size}</div>
                  <div className="text-xs text-gray-500">{resource.downloads} تحميل</div>
                </div>
              </div>

              <div className="flex space-x-2 rtl:space-x-reverse">
                <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse">
                  <Download className="h-4 w-4" />
                  <span>تحميل</span>
                </button>
                <button className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors">
                  <Eye className="h-4 w-4" />
                </button>
              </div>
            </div>
          )
        })}
      </div>

      {filteredResources.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مصادر</h3>
          <p className="text-gray-600">لم يتم العثور على مصادر تطابق معايير البحث</p>
        </div>
      )}
    </div>
  )
}

export default Resources
