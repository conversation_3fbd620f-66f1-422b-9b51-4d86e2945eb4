import React from "react";
import { <PERSON><PERSON>er<PERSON>outer as Router, Routes, Route } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import Navbar from "./components/layout/Navbar";
import Sidebar from "./components/layout/Sidebar";
import Home from "./pages/Home";
import CollegeGuide from "./pages/CollegeGuide";
import GPACalculator from "./pages/GPACalculator";
import Resources from "./pages/Resources";
import AcademicGuidance from "./pages/AcademicGuidance";
import Login from "./pages/Login";
import Profile from "./pages/Profile";
import AttendanceSystem from "./pages/AttendanceSystem";
import TeacherEvaluation from "./pages/TeacherEvaluation";

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex">
          <Sidebar />
          <main className="flex-1 ml-64 p-6">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/college-guide" element={<CollegeGuide />} />
              <Route path="/gpa-calculator" element={<GPACalculator />} />
              <Route path="/resources" element={<Resources />} />
              <Route path="/academic-guidance" element={<AcademicGuidance />} />
              <Route path="/login" element={<Login />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/attendance" element={<AttendanceSystem />} />
              <Route
                path="/teacher-evaluation"
                element={<TeacherEvaluation />}
              />
            </Routes>
          </main>
        </div>
        <Toaster position="top-right" />
      </div>
    </Router>
  );
}

export default App;
