# 🎓 FCI SMARTZONE - منصة كلية الحاسبات والمعلومات الذكية

## 📌 نظرة عامة

FCI SMARTZONE هو مشروع منصة ذكية متكاملة تهدف إلى تحسين تجربة التعليم وإدارة الكلية، عبر توفير جميع الخدمات الأكاديمية والإدارية في واجهة موحدة سهلة الاستخدام.

## 🌟 الخصائص الرئيسية

### 1. دليل الكلية الشامل

- عرض تفصيلي لأقسام الكلية والبرامج الدراسية
- نظام لحساب الساعات المعتمدة وسياسات الغياب
- متطلبات التخرج واللوائح الأكاديمية

### 2. حاسبة المعدل التراكمي (GPA Calculator)

- حساب تلقائي للمعدل التراكمي
- إمكانية المقارنة بين الفصول الدراسية
- تتبع التقدم الأكاديمي

### 3. قاعدة بيانات المصادر التعليمية

- مكتبة مركزية للمحاضرات والكتب
- نظام تقييم للمصادر
- بحث متقدم وتصفية

### 4. التوجيه الأكاديمي والمسارات

- أدلة شاملة للتخصصات والمسارات
- Chatbot ذكي للتوجيه الأكاديمي
- معلومات المسارات المهنية

### 5. نظام الحضور الذكي

- تسجيل الحضور باستخدام QR Code
- التحقق من الموقع الجغرافي (GPS)
- التحقق من البصمة
- تقارير PDF مفصلة

### 6. تقييم أعضاء هيئة التدريس

- تقييم مجهول للمقررات
- نظام النجوم والملاحظات النصية
- تتبع التقييمات السابقة

### 7. إدارة الحسابات والصلاحيات

- حسابات مخصصة (طالب - دكتور - إداري)
- تحكم كامل في الصلاحيات
- ملف شخصي متكامل

## 🛠️ التقنيات المستخدمة

### Frontend

- **React.js 19** - مكتبة واجهة المستخدم
- **React Router** - التنقل بين الصفحات
- **Tailwind CSS** - تصميم الواجهات
- **Vite** - أداة البناء والتطوير

### المكتبات الإضافية

- **Formik & Yup** - إدارة النماذج والتحقق
- **Lucide React** - الأيقونات
- **React Hot Toast** - الإشعارات
- **QR Scanner** - قراءة رموز QR
- **QRCode.js** - إنشاء رموز QR
- **Leaflet & React Leaflet** - الخرائط التفاعلية
- **jsPDF & html2canvas** - إنشاء تقارير PDF

## 🚀 التثبيت والتشغيل

### المتطلبات

- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**

```bash
git clone [repository-url]
cd smartzone
```

2. **تثبيت المكتبات**

```bash
npm install
```

3. **تشغيل المشروع**

```bash
npm run dev
```

4. **فتح المتصفح**

```
http://localhost:5173
```

## 📁 هيكل المشروع

```
src/
├── components/          # المكونات القابلة لإعادة الاستخدام
│   ├── layout/         # مكونات التخطيط (Navbar, Sidebar)
│   └── ui/             # مكونات واجهة المستخدم
├── pages/              # صفحات التطبيق
│   ├── Home.jsx        # الصفحة الرئيسية
│   ├── CollegeGuide.jsx # دليل الكلية
│   ├── GPACalculator.jsx # حاسبة المعدل
│   ├── Resources.jsx    # المصادر التعليمية
│   ├── AcademicGuidance.jsx # التوجيه الأكاديمي
│   ├── AttendanceSystem.jsx # نظام الحضور
│   ├── TeacherEvaluation.jsx # تقييم الأساتذة
│   ├── Login.jsx       # تسجيل الدخول
│   └── Profile.jsx     # الملف الشخصي
├── utils/              # الوظائف المساعدة
├── hooks/              # React Hooks مخصصة
├── context/            # Context API
└── assets/             # الملفات الثابتة
```

## 🎨 التصميم والواجهة

- **تصميم متجاوب** يعمل على جميع الأجهزة
- **دعم اللغة العربية** مع RTL
- **نظام ألوان متسق** مع هوية الكلية
- **تجربة مستخدم سلسة** مع انتقالات ناعمة
- **إمكانية الوصول** للمستخدمين ذوي الاحتياجات الخاصة

## 🔧 الأوامر المتاحة

```bash
# تشغيل المشروع في وضع التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# معاينة البناء
npm run preview

# فحص الكود
npm run lint
```

## 📱 الاستجابة والتوافق

- **الأجهزة المحمولة**: تصميم متجاوب كامل
- **المتصفحات**: Chrome, Firefox, Safari, Edge
- **أنظمة التشغيل**: Windows, macOS, Linux, iOS, Android

## 🔐 الأمان والخصوصية

- **تشفير البيانات** الحساسة
- **التحقق من الهوية** متعدد المستويات
- **حماية من CSRF** و XSS
- **تسجيل العمليات** للمراجعة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👥 الفريق

- **المطور الرئيسي**: [اسم المطور]
- **مصمم الواجهات**: [اسم المصمم]
- **مدير المشروع**: [اسم المدير]

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: [website-url]
- **GitHub**: [github-url]

---

© 2024 FCI SMARTZONE. جميع الحقوق محفوظة.
