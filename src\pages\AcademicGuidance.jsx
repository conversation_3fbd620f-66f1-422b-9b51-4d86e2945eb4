import { useState } from 'react'
import { MessageCircle, Send, User, <PERSON><PERSON>, Comp<PERSON>, TrendingUp, Users, Award } from 'lucide-react'

const AcademicGuidance = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content: 'مرحباً بك في نظام التوجيه الأكاديمي! أنا هنا لمساعدتك في اختيار المسار المناسب لك. ما هي اهتماماتك الأكاديمية؟',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')

  const careerPaths = [
    {
      id: 1,
      title: 'مطور برمجيات',
      description: 'تطوير التطبيقات والأنظمة البرمجية',
      skills: ['JavaScript', 'Python', 'React', 'Node.js'],
      salary: '8,000 - 15,000 ج.م',
      demand: 'عالي',
      icon: '💻'
    },
    {
      id: 2,
      title: 'مهندس أمن معلومات',
      description: 'حماية الأنظمة والشبكات من التهديدات',
      skills: ['Cybersecurity', 'Penetration Testing', 'Network Security'],
      salary: '10,000 - 20,000 ج.م',
      demand: 'عالي جداً',
      icon: '🔒'
    },
    {
      id: 3,
      title: 'عالم بيانات',
      description: 'تحليل البيانات واستخراج الرؤى',
      skills: ['Python', 'Machine Learning', 'SQL', 'Statistics'],
      salary: '12,000 - 25,000 ج.م',
      demand: 'عالي',
      icon: '📊'
    },
    {
      id: 4,
      title: 'مهندس شبكات',
      description: 'تصميم وإدارة الشبكات والبنية التحتية',
      skills: ['Cisco', 'Network Protocols', 'Cloud Computing'],
      salary: '7,000 - 14,000 ج.م',
      demand: 'متوسط',
      icon: '🌐'
    }
  ]

  const specializations = [
    {
      name: 'الذكاء الاصطناعي',
      description: 'تطوير أنظمة ذكية قادرة على التعلم والتفكير',
      courses: ['Machine Learning', 'Deep Learning', 'Computer Vision', 'NLP'],
      prospects: 'ممتاز'
    },
    {
      name: 'أمن المعلومات',
      description: 'حماية البيانات والأنظمة من التهديدات السيبرانية',
      courses: ['Cryptography', 'Network Security', 'Ethical Hacking', 'Digital Forensics'],
      prospects: 'ممتاز'
    },
    {
      name: 'هندسة البرمجيات',
      description: 'تطوير وإدارة المشاريع البرمجية الكبيرة',
      courses: ['Software Architecture', 'Project Management', 'Testing', 'DevOps'],
      prospects: 'جيد جداً'
    },
    {
      name: 'الشبكات والاتصالات',
      description: 'تصميم وإدارة شبكات الحاسوب والاتصالات',
      courses: ['Network Protocols', 'Wireless Networks', 'Cloud Computing', 'IoT'],
      prospects: 'جيد'
    }
  ]

  const sendMessage = () => {
    if (!inputMessage.trim()) return

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])

    // Simulate bot response
    setTimeout(() => {
      const botResponse = {
        id: Date.now() + 1,
        type: 'bot',
        content: getBotResponse(inputMessage),
        timestamp: new Date()
      }
      setMessages(prev => [...prev, botResponse])
    }, 1000)

    setInputMessage('')
  }

  const getBotResponse = (message) => {
    const lowerMessage = message.toLowerCase()
    
    if (lowerMessage.includes('برمجة') || lowerMessage.includes('تطوير')) {
      return 'رائع! إذا كنت مهتماً بالبرمجة، أنصحك بالتركيز على تخصص هندسة البرمجيات أو علوم الحاسب. يمكنك البدء بتعلم لغات مثل Python أو JavaScript.'
    }
    
    if (lowerMessage.includes('أمن') || lowerMessage.includes('حماية')) {
      return 'أمن المعلومات مجال ممتاز ومطلوب بشدة! أنصحك بدراسة تخصص أمن المعلومات والتركيز على الشهادات المهنية مثل CEH و CISSP.'
    }
    
    if (lowerMessage.includes('بيانات') || lowerMessage.includes('تحليل')) {
      return 'علم البيانات مجال مثير! ستحتاج لتعلم Python، SQL، والإحصاء. تخصص نظم المعلومات مع التركيز على تحليل البيانات سيكون مناسباً لك.'
    }
    
    return 'شكراً لك على سؤالك. يمكنني مساعدتك أكثر إذا أخبرتني عن اهتماماتك المحددة في مجال الحاسوب.'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="text-right">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">التوجيه الأكاديمي والمسارات</h1>
          <p className="text-gray-600 text-lg">
            اكتشف مسارك المهني المثالي مع مساعد التوجيه الذكي
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chatbot Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900 text-right flex items-center space-x-2 rtl:space-x-reverse">
              <MessageCircle className="h-6 w-6 text-blue-600" />
              <span>مساعد التوجيه الذكي</span>
            </h2>
          </div>
          
          <div className="h-96 overflow-y-auto p-4 space-y-4">
            {messages.map(message => (
              <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}>
                <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.type === 'user' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-900'
                }`}>
                  <div className="flex items-start space-x-2 rtl:space-x-reverse">
                    {message.type === 'bot' && <Bot className="h-4 w-4 mt-1 text-blue-600" />}
                    {message.type === 'user' && <User className="h-4 w-4 mt-1" />}
                    <p className="text-sm text-right">{message.content}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2 rtl:space-x-reverse">
              <button
                onClick={sendMessage}
                className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Send className="h-5 w-5" />
              </button>
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="اكتب سؤالك هنا..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                dir="rtl"
              />
            </div>
          </div>
        </div>

        {/* Specializations */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">التخصصات المتاحة</h2>
          <div className="space-y-4">
            {specializations.map((spec, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    spec.prospects === 'ممتاز' ? 'bg-green-100 text-green-800' :
                    spec.prospects === 'جيد جداً' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {spec.prospects}
                  </span>
                  <h3 className="font-semibold text-gray-900 text-right">{spec.name}</h3>
                </div>
                <p className="text-gray-600 text-sm mb-3 text-right">{spec.description}</p>
                <div className="text-right">
                  <p className="text-xs text-gray-500 mb-1">المقررات الأساسية:</p>
                  <div className="flex flex-wrap gap-1 justify-end">
                    {spec.courses.map((course, courseIndex) => (
                      <span key={courseIndex} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        {course}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Career Paths */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-right">المسارات المهنية</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {careerPaths.map(path => (
            <div key={path.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="text-right">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    path.demand === 'عالي جداً' ? 'bg-red-100 text-red-800' :
                    path.demand === 'عالي' ? 'bg-green-100 text-green-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    الطلب: {path.demand}
                  </span>
                </div>
                <div className="text-3xl">{path.icon}</div>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2 text-right">{path.title}</h3>
              <p className="text-gray-600 text-sm mb-4 text-right">{path.description}</p>
              
              <div className="mb-4">
                <p className="text-sm font-medium text-gray-900 mb-2 text-right">المهارات المطلوبة:</p>
                <div className="flex flex-wrap gap-1 justify-end">
                  {path.skills.map((skill, index) => (
                    <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">متوسط الراتب:</span> {path.salary}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default AcademicGuidance
