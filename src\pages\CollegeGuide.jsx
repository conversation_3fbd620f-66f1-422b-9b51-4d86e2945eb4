import { useState } from 'react'
import { ChevronDown, ChevronUp, Users, Clock, BookOpen, Award } from 'lucide-react'

const CollegeGuide = () => {
  const [activeSection, setActiveSection] = useState(null)

  const departments = [
    {
      id: 1,
      name: 'علوم الحاسب',
      description: 'قسم علوم الحاسب يركز على الأسس النظرية والعملية للحوسبة',
      students: 450,
      duration: '4 سنوات',
      courses: 42,
      specializations: [
        'الذكاء الاصطناعي',
        'أمن المعلومات',
        'هندسة البرمجيات',
        'الشبكات والاتصالات'
      ],
      requirements: {
        gpa: '3.0',
        hours: '132 ساعة معتمدة',
        projects: 'مشروع تخرج إجباري'
      }
    },
    {
      id: 2,
      name: 'نظم المعلومات',
      description: 'قسم نظم المعلومات يجمع بين التكنولوجيا وإدارة الأعمال',
      students: 380,
      duration: '4 سنوات',
      courses: 40,
      specializations: [
        'نظم المعلومات الإدارية',
        'التجارة الإلكترونية',
        'تحليل البيانات',
        'إدارة قواعد البيانات'
      ],
      requirements: {
        gpa: '2.8',
        hours: '128 ساعة معتمدة',
        projects: 'مشروع تخرج + تدريب ميداني'
      }
    },
    {
      id: 3,
      name: 'تكنولوجيا المعلومات',
      description: 'قسم تكنولوجيا المعلومات يركز على التطبيقات العملية للتكنولوجيا',
      students: 320,
      duration: '4 سنوات',
      courses: 38,
      specializations: [
        'الشبكات والأمان',
        'تطوير الويب',
        'إدارة النظم',
        'الحوسبة السحابية'
      ],
      requirements: {
        gpa: '2.7',
        hours: '130 ساعة معتمدة',
        projects: 'مشروع تخرج + شهادات مهنية'
      }
    }
  ]

  const academicRegulations = [
    {
      title: 'نظام الدرجات',
      content: [
        'A: 90-100 (ممتاز)',
        'B+: 85-89 (جيد جداً مرتفع)',
        'B: 80-84 (جيد جداً)',
        'C+: 75-79 (جيد مرتفع)',
        'C: 70-74 (جيد)',
        'D+: 65-69 (مقبول مرتفع)',
        'D: 60-64 (مقبول)',
        'F: أقل من 60 (راسب)'
      ]
    },
    {
      title: 'سياسة الغياب',
      content: [
        'الحد الأقصى للغياب: 25% من إجمالي المحاضرات',
        'تجاوز نسبة الغياب يؤدي إلى الحرمان من دخول الامتحان',
        'الغياب بعذر مقبول لا يحتسب ضمن نسبة الغياب',
        'يجب تقديم العذر خلال أسبوع من تاريخ الغياب'
      ]
    },
    {
      title: 'متطلبات التخرج',
      content: [
        'إنهاء جميع المقررات المطلوبة بنجاح',
        'الحصول على معدل تراكمي لا يقل عن 2.0',
        'إنجاز مشروع التخرج بتقدير لا يقل عن C',
        'استيفاء متطلبات التدريب الميداني (حسب القسم)'
      ]
    }
  ]

  const toggleSection = (sectionId) => {
    setActiveSection(activeSection === sectionId ? null : sectionId)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="text-right">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">دليل كلية الحاسبات والمعلومات</h1>
          <p className="text-gray-600 text-lg">
            دليل شامل للأقسام الأكاديمية، البرامج الدراسية، واللوائح الأكاديمية
          </p>
        </div>
      </div>

      {/* Departments */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-right">الأقسام الأكاديمية</h2>
        
        <div className="space-y-4">
          {departments.map((dept) => (
            <div key={dept.id} className="border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleSection(`dept-${dept.id}`)}
                className="w-full p-4 text-right bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{dept.name}</h3>
                  <p className="text-gray-600 mt-1">{dept.description}</p>
                </div>
                {activeSection === `dept-${dept.id}` ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
              </button>
              
              {activeSection === `dept-${dept.id}` && (
                <div className="p-6 bg-white">
                  {/* Department Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Users className="h-5 w-5 text-blue-500" />
                      <div className="text-right">
                        <p className="text-sm text-gray-600">عدد الطلاب</p>
                        <p className="font-semibold">{dept.students}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Clock className="h-5 w-5 text-green-500" />
                      <div className="text-right">
                        <p className="text-sm text-gray-600">مدة الدراسة</p>
                        <p className="font-semibold">{dept.duration}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <BookOpen className="h-5 w-5 text-purple-500" />
                      <div className="text-right">
                        <p className="text-sm text-gray-600">عدد المقررات</p>
                        <p className="font-semibold">{dept.courses}</p>
                      </div>
                    </div>
                  </div>

                  {/* Specializations */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3 text-right">التخصصات المتاحة:</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {dept.specializations.map((spec, index) => (
                        <div key={index} className="bg-blue-50 text-blue-800 px-3 py-2 rounded-lg text-right">
                          {spec}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Requirements */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3 text-right">متطلبات التخرج:</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="space-y-2 text-right">
                        <p><span className="font-medium">المعدل المطلوب:</span> {dept.requirements.gpa}</p>
                        <p><span className="font-medium">الساعات المعتمدة:</span> {dept.requirements.hours}</p>
                        <p><span className="font-medium">المتطلبات الإضافية:</span> {dept.requirements.projects}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Academic Regulations */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-right">اللوائح الأكاديمية</h2>
        
        <div className="space-y-4">
          {academicRegulations.map((regulation, index) => (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleSection(`reg-${index}`)}
                className="w-full p-4 text-right bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
              >
                <h3 className="text-lg font-semibold text-gray-900">{regulation.title}</h3>
                {activeSection === `reg-${index}` ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
              </button>
              
              {activeSection === `reg-${index}` && (
                <div className="p-6 bg-white">
                  <ul className="space-y-2 text-right">
                    {regulation.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start space-x-2 rtl:space-x-reverse">
                        <span className="text-blue-500 mt-1">•</span>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default CollegeGuide
