import { useState } from 'react'
import { Star, Send, Eye, BarChart3, MessageSquare } from 'lucide-react'
import toast from 'react-hot-toast'

const TeacherEvaluation = () => {
  const [selectedCourse, setSelectedCourse] = useState(null)
  const [ratings, setRatings] = useState({
    teaching_quality: 0,
    course_content: 0,
    interaction: 0,
    punctuality: 0,
    overall: 0
  })
  const [comment, setComment] = useState('')

  const courses = [
    {
      id: 1,
      name: 'البرمجة المتقدمة',
      teacher: 'د. أحمد محمد',
      code: 'CS301',
      semester: 'الفصل الأول 2024',
      canEvaluate: true,
      evaluated: false
    },
    {
      id: 2,
      name: 'قواعد البيانات',
      teacher: 'د. فاطمة علي',
      code: 'CS205',
      semester: 'الفصل الأول 2024',
      canEvaluate: true,
      evaluated: true
    },
    {
      id: 3,
      name: 'الشبكات والاتصالات',
      teacher: 'د. محمد سعد',
      code: 'CS310',
      semester: 'الفصل الأول 2024',
      canEvaluate: false,
      evaluated: false
    }
  ]

  const evaluationCriteria = [
    {
      key: 'teaching_quality',
      label: 'جودة التدريس',
      description: 'وضوح الشرح وطريقة التدريس'
    },
    {
      key: 'course_content',
      label: 'محتوى المقرر',
      description: 'جودة وتنظيم محتوى المقرر'
    },
    {
      key: 'interaction',
      label: 'التفاعل مع الطلاب',
      description: 'مدى تفاعل الأستاذ مع الطلاب'
    },
    {
      key: 'punctuality',
      label: 'الالتزام بالمواعيد',
      description: 'الحضور في الوقت المحدد'
    },
    {
      key: 'overall',
      label: 'التقييم العام',
      description: 'التقييم الشامل للمقرر والأستاذ'
    }
  ]

  const previousEvaluations = [
    {
      course: 'قواعد البيانات',
      teacher: 'د. فاطمة علي',
      ratings: {
        teaching_quality: 4,
        course_content: 5,
        interaction: 4,
        punctuality: 5,
        overall: 4
      },
      comment: 'مقرر ممتاز وشرح واضح',
      date: '2024-01-15'
    }
  ]

  const handleRatingChange = (criterion, rating) => {
    setRatings(prev => ({
      ...prev,
      [criterion]: rating
    }))
  }

  const handleSubmitEvaluation = () => {
    if (!selectedCourse) {
      toast.error('يرجى اختيار مقرر للتقييم')
      return
    }

    const hasAllRatings = Object.values(ratings).every(rating => rating > 0)
    if (!hasAllRatings) {
      toast.error('يرجى تقييم جميع المعايير')
      return
    }

    // Simulate submission
    toast.success('تم إرسال التقييم بنجاح')
    setSelectedCourse(null)
    setRatings({
      teaching_quality: 0,
      course_content: 0,
      interaction: 0,
      punctuality: 0,
      overall: 0
    })
    setComment('')
  }

  const renderStars = (criterion, currentRating) => {
    return (
      <div className="flex space-x-1 rtl:space-x-reverse">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => handleRatingChange(criterion, star)}
            className="focus:outline-none"
          >
            <Star
              className={`h-6 w-6 ${
                star <= currentRating
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-300 hover:text-yellow-400'
              } transition-colors`}
            />
          </button>
        ))}
      </div>
    )
  }

  const renderDisplayStars = (rating) => {
    return (
      <div className="flex space-x-1 rtl:space-x-reverse">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="text-right">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">تقييم أعضاء هيئة التدريس</h1>
          <p className="text-gray-600 text-lg">
            قيم أداء أعضاء هيئة التدريس والمقررات الدراسية
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Course Selection */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">اختر المقرر للتقييم</h2>
          <div className="space-y-3">
            {courses.map((course) => (
              <div
                key={course.id}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedCourse?.id === course.id
                    ? 'border-blue-500 bg-blue-50'
                    : course.canEvaluate
                    ? 'border-gray-200 hover:border-gray-300'
                    : 'border-gray-100 bg-gray-50 cursor-not-allowed'
                }`}
                onClick={() => course.canEvaluate && setSelectedCourse(course)}
              >
                <div className="text-right">
                  <h3 className="font-semibold text-gray-900">{course.name}</h3>
                  <p className="text-sm text-gray-600">{course.teacher}</p>
                  <p className="text-xs text-gray-500">{course.code} - {course.semester}</p>
                  <div className="mt-2">
                    {course.evaluated ? (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        تم التقييم
                      </span>
                    ) : course.canEvaluate ? (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        متاح للتقييم
                      </span>
                    ) : (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                        غير متاح
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Evaluation Form */}
        <div className="lg:col-span-2 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          {selectedCourse ? (
            <div>
              <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 text-right">
                  تقييم: {selectedCourse.name}
                </h2>
                <p className="text-gray-600 text-right">
                  الأستاذ: {selectedCourse.teacher}
                </p>
              </div>

              <div className="space-y-6">
                {evaluationCriteria.map((criterion) => (
                  <div key={criterion.key} className="border-b border-gray-100 pb-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="text-right">
                        <span className="text-lg font-medium text-gray-900">
                          {ratings[criterion.key]}/5
                        </span>
                      </div>
                      <div className="text-right">
                        <h3 className="font-semibold text-gray-900">{criterion.label}</h3>
                        <p className="text-sm text-gray-600">{criterion.description}</p>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      {renderStars(criterion.key, ratings[criterion.key])}
                    </div>
                  </div>
                ))}

                {/* Comment Section */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 text-right">
                    تعليقات إضافية (اختياري)
                  </label>
                  <textarea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                    placeholder="اكتب تعليقاتك هنا..."
                    dir="rtl"
                  />
                </div>

                {/* Submit Button */}
                <button
                  onClick={handleSubmitEvaluation}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
                >
                  <Send className="h-5 w-5" />
                  <span>إرسال التقييم</span>
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">اختر مقرراً للتقييم</h3>
              <p className="text-gray-600">
                اختر أحد المقررات من القائمة الجانبية لبدء التقييم
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Previous Evaluations */}
      {previousEvaluations.length > 0 && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">التقييمات السابقة</h2>
          <div className="space-y-4">
            {previousEvaluations.map((evaluation, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-4">
                  <span className="text-sm text-gray-500">{evaluation.date}</span>
                  <div className="text-right">
                    <h3 className="font-semibold text-gray-900">{evaluation.course}</h3>
                    <p className="text-sm text-gray-600">{evaluation.teacher}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                  {evaluationCriteria.map((criterion) => (
                    <div key={criterion.key} className="text-center">
                      <p className="text-xs text-gray-600 mb-1">{criterion.label}</p>
                      {renderDisplayStars(evaluation.ratings[criterion.key])}
                      <p className="text-xs text-gray-500 mt-1">
                        {evaluation.ratings[criterion.key]}/5
                      </p>
                    </div>
                  ))}
                </div>

                {evaluation.comment && (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-start space-x-2 rtl:space-x-reverse">
                      <MessageSquare className="h-4 w-4 text-gray-500 mt-1" />
                      <p className="text-sm text-gray-700 text-right">{evaluation.comment}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default TeacherEvaluation
