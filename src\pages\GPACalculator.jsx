import { useState, useEffect } from 'react'
import { Plus, Trash2, <PERSON><PERSON><PERSON>, TrendingUp, BookOpen, Award } from 'lucide-react'
import toast from 'react-hot-toast'

const GPACalculator = () => {
  const [courses, setCourses] = useState([
    { id: 1, name: '', grade: '', hours: '', points: 0 }
  ])
  const [currentGPA, setCurrentGPA] = useState(0)
  const [totalHours, setTotalHours] = useState(0)
  const [totalPoints, setTotalPoints] = useState(0)

  const gradePoints = {
    'A': 4.0,
    'B+': 3.5,
    'B': 3.0,
    'C+': 2.5,
    'C': 2.0,
    'D+': 1.5,
    'D': 1.0,
    'F': 0.0
  }

  const gradeOptions = [
    { value: 'A', label: 'A (ممتاز)', points: 4.0 },
    { value: 'B+', label: 'B+ (جيد جداً مرتفع)', points: 3.5 },
    { value: 'B', label: 'B (جيد جداً)', points: 3.0 },
    { value: 'C+', label: 'C+ (جيد مرتفع)', points: 2.5 },
    { value: 'C', label: 'C (جيد)', points: 2.0 },
    { value: 'D+', label: 'D+ (مقبول مرتفع)', points: 1.5 },
    { value: 'D', label: 'D (مقبول)', points: 1.0 },
    { value: 'F', label: 'F (راسب)', points: 0.0 }
  ]

  useEffect(() => {
    calculateGPA()
  }, [courses])

  const calculateGPA = () => {
    let totalHrs = 0
    let totalPts = 0

    courses.forEach(course => {
      if (course.grade && course.hours) {
        const hours = parseFloat(course.hours)
        const points = gradePoints[course.grade] || 0
        totalHrs += hours
        totalPts += hours * points
      }
    })

    setTotalHours(totalHrs)
    setTotalPoints(totalPts)
    setCurrentGPA(totalHrs > 0 ? (totalPts / totalHrs).toFixed(2) : 0)
  }

  const addCourse = () => {
    const newCourse = {
      id: Date.now(),
      name: '',
      grade: '',
      hours: '',
      points: 0
    }
    setCourses([...courses, newCourse])
  }

  const removeCourse = (id) => {
    if (courses.length > 1) {
      setCourses(courses.filter(course => course.id !== id))
    } else {
      toast.error('يجب أن يكون هناك مقرر واحد على الأقل')
    }
  }

  const updateCourse = (id, field, value) => {
    setCourses(courses.map(course => {
      if (course.id === id) {
        const updatedCourse = { ...course, [field]: value }
        if (field === 'grade' || field === 'hours') {
          const hours = parseFloat(updatedCourse.hours) || 0
          const points = gradePoints[updatedCourse.grade] || 0
          updatedCourse.points = hours * points
        }
        return updatedCourse
      }
      return course
    }))
  }

  const clearAll = () => {
    setCourses([{ id: 1, name: '', grade: '', hours: '', points: 0 }])
    toast.success('تم مسح جميع البيانات')
  }

  const getGPAStatus = (gpa) => {
    if (gpa >= 3.7) return { text: 'ممتاز', color: 'text-green-600', bg: 'bg-green-50' }
    if (gpa >= 3.0) return { text: 'جيد جداً', color: 'text-blue-600', bg: 'bg-blue-50' }
    if (gpa >= 2.5) return { text: 'جيد', color: 'text-yellow-600', bg: 'bg-yellow-50' }
    if (gpa >= 2.0) return { text: 'مقبول', color: 'text-orange-600', bg: 'bg-orange-50' }
    return { text: 'ضعيف', color: 'text-red-600', bg: 'bg-red-50' }
  }

  const status = getGPAStatus(currentGPA)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="text-right">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">حاسبة المعدل التراكمي</h1>
          <p className="text-gray-600 text-lg">
            احسب معدلك التراكمي بدقة وتابع تقدمك الأكاديمي
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Course Input Section */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex justify-between items-center mb-6">
              <button
                onClick={clearAll}
                className="text-red-600 hover:text-red-700 text-sm font-medium"
              >
                مسح الكل
              </button>
              <h2 className="text-xl font-bold text-gray-900">إدخال المقررات</h2>
            </div>

            <div className="space-y-4">
              {courses.map((course, index) => (
                <div key={course.id} className="grid grid-cols-12 gap-4 items-center p-4 bg-gray-50 rounded-lg">
                  <div className="col-span-1 text-center">
                    <span className="text-sm font-medium text-gray-600">{index + 1}</span>
                  </div>
                  
                  <div className="col-span-4">
                    <input
                      type="text"
                      placeholder="اسم المقرر"
                      value={course.name}
                      onChange={(e) => updateCourse(course.id, 'name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                      dir="rtl"
                    />
                  </div>

                  <div className="col-span-3">
                    <select
                      value={course.grade}
                      onChange={(e) => updateCourse(course.id, 'grade', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                    >
                      <option value="">اختر التقدير</option>
                      {gradeOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="col-span-2">
                    <input
                      type="number"
                      placeholder="الساعات"
                      value={course.hours}
                      onChange={(e) => updateCourse(course.id, 'hours', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center"
                      min="0"
                      step="0.5"
                    />
                  </div>

                  <div className="col-span-1">
                    <span className="text-sm font-medium text-gray-600">
                      {course.points.toFixed(1)}
                    </span>
                  </div>

                  <div className="col-span-1">
                    <button
                      onClick={() => removeCourse(course.id)}
                      className="text-red-500 hover:text-red-700 p-1"
                      disabled={courses.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <button
              onClick={addCourse}
              className="mt-4 w-full flex items-center justify-center space-x-2 rtl:space-x-reverse py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-500 hover:text-blue-600 transition-colors"
            >
              <Plus className="h-5 w-5" />
              <span>إضافة مقرر جديد</span>
            </button>
          </div>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {/* GPA Display */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="text-center">
              <div className="mb-4">
                <Calculator className="h-12 w-12 text-blue-600 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">المعدل التراكمي</h3>
              <div className="text-4xl font-bold text-blue-600 mb-2">{currentGPA}</div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${status.color} ${status.bg}`}>
                {status.text}
              </div>
            </div>
          </div>

          {/* Statistics */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 text-right">الإحصائيات</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-green-600">{totalHours}</span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <BookOpen className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">إجمالي الساعات</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-purple-600">{totalPoints.toFixed(1)}</span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <TrendingUp className="h-5 w-5 text-purple-600" />
                  <span className="text-gray-700">إجمالي النقاط</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-orange-600">{courses.length}</span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Award className="h-5 w-5 text-orange-600" />
                  <span className="text-gray-700">عدد المقررات</span>
                </div>
              </div>
            </div>
          </div>

          {/* GPA Scale */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 text-right">مقياس التقديرات</h3>
            <div className="space-y-2">
              {gradeOptions.map(grade => (
                <div key={grade.value} className="flex justify-between items-center py-1">
                  <span className="font-medium text-gray-900">{grade.points.toFixed(1)}</span>
                  <span className="text-gray-600">{grade.label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GPACalculator
