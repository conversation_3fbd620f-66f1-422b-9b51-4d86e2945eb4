{"name": "smartzone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"formik": "^2.4.6", "framer-motion": "^12.23.12", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lucide-react": "^0.539.0", "qr-scanner": "^1.4.2", "qrcode.js": "^0.0.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.5.2", "react-leaflet": "^5.0.0", "react-router-dom": "^7.8.0", "yup": "^1.7.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.1.0"}}