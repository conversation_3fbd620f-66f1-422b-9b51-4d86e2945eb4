import { Link } from 'react-router-dom'
import {
  BookOpen,
  Calculator,
  Library,
  Compass,
  QrCode,
  Star,
  TrendingUp,
  Calendar,
  Bell,
  Users
} from 'lucide-react'

const Home = () => {
  const quickActions = [
    {
      title: 'حاسبة المعدل',
      description: 'احسب معدلك التراكمي',
      icon: Calculator,
      path: '/gpa-calculator',
      color: 'bg-blue-500'
    },
    {
      title: 'المصادر التعليمية',
      description: 'تصفح المحاضرات والكتب',
      icon: Library,
      path: '/resources',
      color: 'bg-green-500'
    },
    {
      title: 'نظام الحضور',
      description: 'سجل حضورك',
      icon: QrCode,
      path: '/attendance',
      color: 'bg-purple-500'
    },
    {
      title: 'التوجيه الأكاديمي',
      description: 'اكتشف مسارك المهني',
      icon: Compass,
      path: '/academic-guidance',
      color: 'bg-orange-500'
    }
  ]

  const recentActivities = [
    {
      title: 'تم رفع محاضرة جديدة في مادة البرمجة المتقدمة',
      time: 'منذ ساعتين',
      type: 'محاضرة'
    },
    {
      title: 'تذكير: امتحان مادة قواعد البيانات غداً',
      time: 'منذ 4 ساعات',
      type: 'تذكير'
    },
    {
      title: 'تم تحديث جدول المحاضرات للأسبوع القادم',
      time: 'منذ يوم',
      type: 'تحديث'
    }
  ]

  const stats = [
    {
      title: 'المعدل التراكمي',
      value: '3.85',
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'الساعات المكتملة',
      value: '120',
      icon: BookOpen,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'المحاضرات هذا الأسبوع',
      value: '18',
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'الإشعارات الجديدة',
      value: '5',
      icon: Bell,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white">
        <div className="text-right">
          <h1 className="text-3xl font-bold mb-2">مرحباً بك في FCI SMARTZONE</h1>
          <p className="text-blue-100 text-lg">
            منصتك الذكية لإدارة رحلتك الأكاديمية في كلية الحاسبات والمعلومات
          </p>
        </div>
        <div className="mt-6 flex justify-end">
          <Link
            to="/college-guide"
            className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
          >
            استكشف دليل الكلية
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div className="text-right">
                  <p className="text-sm text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">الإجراءات السريعة</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => {
            const Icon = action.icon
            return (
              <Link
                key={index}
                to={action.path}
                className="group p-4 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200"
              >
                <div className="text-center">
                  <div className={`inline-flex p-3 rounded-lg ${action.color} text-white mb-3`}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">{action.title}</h3>
                  <p className="text-sm text-gray-600">{action.description}</p>
                </div>
              </Link>
            )
          })}
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">الأنشطة الأخيرة</h2>
        <div className="space-y-4">
          {recentActivities.map((activity, index) => (
            <div key={index} className="flex items-start space-x-4 rtl:space-x-reverse p-4 rounded-lg bg-gray-50">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              </div>
              <div className="flex-1 text-right">
                <p className="text-gray-900 font-medium">{activity.title}</p>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-gray-500">{activity.time}</span>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {activity.type}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Home
