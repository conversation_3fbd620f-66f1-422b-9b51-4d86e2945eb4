import { useState, useEffect } from 'react'
import { QrCode, MapPin, Fingerprint, Clock, Users, Download, Eye } from 'lucide-react'
import QRCode from 'qrcode.js'
import toast from 'react-hot-toast'

const AttendanceSystem = () => {
  const [userRole, setUserRole] = useState('teacher') // teacher or student
  const [activeSession, setActiveSession] = useState(null)
  const [qrCodeData, setQrCodeData] = useState('')
  const [sessionDuration, setSessionDuration] = useState(15)
  const [currentLocation, setCurrentLocation] = useState(null)

  const [attendanceData, setAttendanceData] = useState([
    {
      id: 1,
      studentName: 'أحمد محمد علي',
      studentId: '2021170001',
      checkInTime: '09:15:23',
      location: { lat: 30.0444, lng: 31.2357 },
      distance: '5 متر',
      fingerprintVerified: true,
      status: 'حاضر'
    },
    {
      id: 2,
      studentName: 'فاطمة أحمد',
      studentId: '2021170002',
      checkInTime: '09:16:45',
      location: { lat: 30.0445, lng: 31.2358 },
      distance: '3 متر',
      fingerprintVerified: true,
      status: 'حاضر'
    },
    {
      id: 3,
      studentName: 'محمد سعد',
      studentId: '2021170003',
      checkInTime: '09:18:12',
      location: { lat: 30.0446, lng: 31.2359 },
      distance: '8 متر',
      fingerprintVerified: false,
      status: 'متأخر'
    }
  ])

  const sessions = [
    {
      id: 1,
      course: 'البرمجة المتقدمة',
      date: '2024-08-09',
      time: '09:00',
      room: 'قاعة 101',
      attendees: 25,
      status: 'مكتملة'
    },
    {
      id: 2,
      course: 'قواعد البيانات',
      date: '2024-08-08',
      time: '11:00',
      room: 'قاعة 205',
      attendees: 30,
      status: 'مكتملة'
    }
  ]

  useEffect(() => {
    // Get current location
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          })
        },
        (error) => {
          console.error('Error getting location:', error)
          toast.error('لا يمكن الحصول على الموقع الحالي')
        }
      )
    }
  }, [])

  const generateQRCode = () => {
    const sessionData = {
      sessionId: Date.now(),
      timestamp: new Date().toISOString(),
      location: currentLocation,
      duration: sessionDuration
    }
    
    const qrData = JSON.stringify(sessionData)
    setQrCodeData(qrData)
    setActiveSession(sessionData)
    
    toast.success('تم إنشاء جلسة الحضور بنجاح')
    
    // Auto-close session after duration
    setTimeout(() => {
      setActiveSession(null)
      setQrCodeData('')
      toast.info('انتهت جلسة الحضور')
    }, sessionDuration * 60 * 1000)
  }

  const stopSession = () => {
    setActiveSession(null)
    setQrCodeData('')
    toast.success('تم إنهاء جلسة الحضور')
  }

  const generateReport = () => {
    toast.success('جاري تحضير التقرير...')
    // Here you would generate PDF report
  }

  const simulateFingerprint = () => {
    toast.success('تم التحقق من البصمة بنجاح')
  }

  if (userRole === 'teacher') {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="text-right">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">نظام الحضور - واجهة المدرس</h1>
            <p className="text-gray-600 text-lg">
              إدارة جلسات الحضور وتتبع حضور الطلاب
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* QR Code Generation */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">إنشاء جلسة حضور</h2>
            
            {!activeSession ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 text-right">
                    مدة الجلسة (بالدقائق)
                  </label>
                  <select
                    value={sessionDuration}
                    onChange={(e) => setSessionDuration(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                  >
                    <option value={5}>5 دقائق</option>
                    <option value={10}>10 دقائق</option>
                    <option value={15}>15 دقيقة</option>
                    <option value={20}>20 دقيقة</option>
                    <option value={30}>30 دقيقة</option>
                  </select>
                </div>
                
                <button
                  onClick={generateQRCode}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
                >
                  <QrCode className="h-5 w-5" />
                  <span>إنشاء رمز QR للحضور</span>
                </button>
              </div>
            ) : (
              <div className="text-center">
                <div className="mb-4">
                  <div className="w-64 h-64 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
                    <QrCode className="h-32 w-32 text-gray-400" />
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    رمز QR للحضور (صالح لمدة {sessionDuration} دقيقة)
                  </p>
                </div>
                
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">
                    <Clock className="inline h-4 w-4 ml-1" />
                    بدأت الجلسة: {new Date(activeSession.timestamp).toLocaleTimeString('ar-EG')}
                  </p>
                  {currentLocation && (
                    <p className="text-sm text-gray-600">
                      <MapPin className="inline h-4 w-4 ml-1" />
                      الموقع: {currentLocation.lat.toFixed(4)}, {currentLocation.lng.toFixed(4)}
                    </p>
                  )}
                </div>
                
                <button
                  onClick={stopSession}
                  className="bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
                >
                  إنهاء الجلسة
                </button>
              </div>
            )}
          </div>

          {/* Live Attendance */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex justify-between items-center mb-6">
              <button
                onClick={generateReport}
                className="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
              >
                <Download className="h-4 w-4" />
                <span>تحميل التقرير</span>
              </button>
              <h2 className="text-xl font-bold text-gray-900 text-right">الحضور المباشر</h2>
            </div>
            
            <div className="space-y-3">
              {attendanceData.map((student) => (
                <div key={student.id} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <span className={`w-3 h-3 rounded-full ${
                        student.status === 'حاضر' ? 'bg-green-500' : 'bg-yellow-500'
                      }`}></span>
                      <span className="text-sm text-gray-600">{student.status}</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">{student.studentName}</p>
                      <p className="text-sm text-gray-600">{student.studentId}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <p><Clock className="inline h-3 w-3 ml-1" />وقت الوصول: {student.checkInTime}</p>
                      <p><MapPin className="inline h-3 w-3 ml-1" />المسافة: {student.distance}</p>
                    </div>
                    <div className="text-right">
                      <p>
                        <Fingerprint className={`inline h-3 w-3 ml-1 ${
                          student.fingerprintVerified ? 'text-green-500' : 'text-red-500'
                        }`} />
                        البصمة: {student.fingerprintVerified ? 'مؤكدة' : 'غير مؤكدة'}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Previous Sessions */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">الجلسات السابقة</h2>
          <div className="overflow-x-auto">
            <table className="w-full text-right">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="py-3 px-4 text-right font-semibold text-gray-900">الإجراءات</th>
                  <th className="py-3 px-4 text-right font-semibold text-gray-900">الحالة</th>
                  <th className="py-3 px-4 text-right font-semibold text-gray-900">عدد الحاضرين</th>
                  <th className="py-3 px-4 text-right font-semibold text-gray-900">القاعة</th>
                  <th className="py-3 px-4 text-right font-semibold text-gray-900">الوقت</th>
                  <th className="py-3 px-4 text-right font-semibold text-gray-900">التاريخ</th>
                  <th className="py-3 px-4 text-right font-semibold text-gray-900">المقرر</th>
                </tr>
              </thead>
              <tbody>
                {sessions.map((session) => (
                  <tr key={session.id} className="border-b border-gray-100">
                    <td className="py-3 px-4">
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <button className="text-blue-600 hover:text-blue-700">
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-700">
                          <Download className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                        {session.status}
                      </span>
                    </td>
                    <td className="py-3 px-4">{session.attendees}</td>
                    <td className="py-3 px-4">{session.room}</td>
                    <td className="py-3 px-4">{session.time}</td>
                    <td className="py-3 px-4">{session.date}</td>
                    <td className="py-3 px-4 font-semibold">{session.course}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    )
  }

  // Student View
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="text-right">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">نظام الحضور - واجهة الطالب</h1>
          <p className="text-gray-600 text-lg">
            سجل حضورك باستخدام رمز QR والتحقق من البصمة
          </p>
        </div>
      </div>

      {/* QR Scanner & Fingerprint */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">مسح رمز QR</h2>
          <div className="text-center">
            <div className="w-64 h-64 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              <QrCode className="h-32 w-32 text-gray-400" />
            </div>
            <p className="text-gray-600 mb-4">وجه الكاميرا نحو رمز QR للحضور</p>
            <button className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
              تشغيل الكاميرا
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h2 className="text-xl font-bold text-gray-900 mb-6 text-right">التحقق من البصمة</h2>
          <div className="text-center">
            <div className="w-32 h-32 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Fingerprint className="h-16 w-16 text-gray-400" />
            </div>
            <p className="text-gray-600 mb-4">ضع إصبعك على مستشعر البصمة</p>
            <button 
              onClick={simulateFingerprint}
              className="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
            >
              التحقق من البصمة
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AttendanceSystem
